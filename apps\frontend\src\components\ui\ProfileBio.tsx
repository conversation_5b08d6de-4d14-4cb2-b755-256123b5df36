import Image from "next/image";

export interface ProfileBioProps {
  name: string;
  authorRole: string;
  authorImageUrl?: string;
  authorImageAlt?: string;
}

const ProfileBio = ({ name, authorRole, authorImageUrl, authorImageAlt }: ProfileBioProps) => {

  const defaultImageUrl = "/images/default-profile-pic.png";
  const defaultImageAlt = "Default Profile Icon";
  
  return (
    <div className="flex flex-row gap-2">
        {/* Profile Picture */}
        <div className="relative w-[48px] h-[48px] rounded-full">
            <Image
                src={authorImageUrl || defaultImageUrl}
                alt={authorImageAlt || defaultImageAlt}
                fill
                className="object-cover"
            />
        </div>
        {/* Name and Role */}
        <div className="flex flex-col">
            <p className="text-default-900 font-semibold text-sm">{name}</p>
            <p className="text-default-600 text-xs">{authorRole}</p>
        </div>
    </div>
  );
};

export default ProfileBio;
//end