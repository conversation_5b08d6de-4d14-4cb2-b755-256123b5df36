{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo -p 3002", "build": "next build", "start": "next start --port 3002", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroui/button": "^2.2.16", "@heroui/input": "^2.4.16", "@heroui/popover": "^2.3.16", "@heroui/react": "^2.7.5", "@heroui/tooltip": "^2.2.13", "framer-motion": "^12.5.0", "lucide-react": "^0.483.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-player": "^2.13.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "^15.3.3", "next": "^15.2.4", "postcss": "^8.4.31", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0", "validator": "^13.15.15"}}