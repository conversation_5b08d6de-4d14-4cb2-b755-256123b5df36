import React from "react";
import ArticleCard, { ArticleCardProps } from "./ui/ArticleCard";
import SecondaryBtn from "./ui/SecondaryBtn";

const LatestArticles = () => {
    const articles: ArticleCardProps[] = [
        {
            id: 1,
            title: "Starting a relationship with God is more than just prayer",
            description: "What does it mean to follow God? What does it mean to pray. " + 
                            "Learn practical steps for spiritual growth.",
            date: "2025-02-18",
            imageUrl: "/images/hands-image.png",
            imageAlt: "Hands folded for prayer",
            category: "New to Faith",
            author: {
                name: "<PERSON>",
                authorImageUrl: "/images/Fred-Profile-Pic.png",
                authorImageAlt: "<PERSON>'s profile picture",
                authorRole: "Content Creator",
            },
        },
        {
            id: 2,
            title: "What is repentance?",
            description: "Repentance in the Bible is a heartfelt recognition of sin, " + 
                            "turning away from that sin, and a reorientation toward God.",
            date: "2025-02-18",
            imageUrl: "/images/people.png",
            imageAlt: "Two guys sitted by a table",
            category: "Devotionals",
            author: {
                name: "<PERSON>",
                authorImageUrl: "/images/John-profile-pic.png",
                authorImageAlt: "<PERSON>'s profile picture",
                authorRole: "Website Administrator",
            },
        },
        {
            id: 3,
            title: "Sunday Worship",
            description: "Worshipping God is much more than attending church and " + 
                            "engaging in worship on Sundays.",
            date: "2025-02-18",
            imageUrl: "/images/crowd-hands.png",
            imageAlt: "People worshiping in a crowd",
            category: "Devotionals",
            author: {
                name: "Bobby Bob",
                authorRole: "Website Administrator",
            },
        },
    ];

    return (
        <div className="lg:px-28 md:px-12 px-4 py-12 lg:py-20 md:py-16 flex justify-center items-start">
            <div className="flex flex-col gap-3">
            <div className="flex flex-row justify-between items-center">
                <h3 className="text-left text-default-900 text-4xl font-semibold font-['Poppins'] leading-[60px]">
                    Latest articles
                </h3>
                <SecondaryBtn text="See all" color="text-default-900" />
                
            </div>
                <div className="flex flex-wrap gap-4">
                    {articles.map((article) => (
                        <ArticleCard key={article.id} {...article} />
                    ))}
                </div>
            </div>
        </div>
    )
}

export default LatestArticles;
//end