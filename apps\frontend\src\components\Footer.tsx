"use client";

import { But<PERSON> } from "@heroui/button";
import { JSX } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Image from "next/image";
import Link from "next/link";

interface FooterLink {
  label: string;
  href: string;
}

interface SocialLink {
  icon: JSX.Element;
  label: string;
  href: string;
}

interface FooterProps {
  linksBySection: { [sectionName: string]: FooterLink[] };
  newsletterSignupUrl: string;
  socialLinks: SocialLink[];
  legalLinks: FooterLink[];
  branding: {
    logo: {
      src: string;
      alt: string;
      width: number;
      height: number;
    };
    name: string;
    tagline: string;
  };
  journeySection: {
    title: string;
    subtitle: string;
    actions: Array<{
      icon: JSX.Element;
      label: string;
      href: string;
      alt: string;
    }>;
  };
  newsletter: {
    title: string;
    description: string;
    placeholder: string;
    buttonText: string;
    privacyText: string;
    tosHref: string;
    privacyHref: string;
  };
  copyright: string;
}

// Basic sanitization utilities
const sanitizeHref = (href: string): string => {
  if (typeof href !== "string") return "#";
  const allowedProtocols = ["http:", "https:", "mailto:", "tel:"];
  try {
    if (href.startsWith("/") || href.startsWith("#")) return href;
    const url = new URL(href);
    return allowedProtocols.includes(url.protocol) ? href : "#";
  } catch {
    return href.startsWith("/") ? href : "#";
  }
};

const sanitizeText = (text: string): string =>
  typeof text === "string" ? text.replace(/<[^>]*>/g, "") : "";

export default function Footer({
  linksBySection,
  newsletterSignupUrl,
  socialLinks,
  legalLinks,
  branding,
  journeySection,
  newsletter,
  copyright,
}: FooterProps) {
  return (
    <footer className="px-4 sm:px-6 md:px-8 lg:px-12 pt-16 sm:pt-20 md:pt-24 lg:pt-32 pb-12 md:pb-16 lg:pb-20 bg-secondary-900">
      <div className="flex flex-col gap-8 sm:gap-10 md:gap-12 lg:gap-16">
        <div className="flex flex-col xl:flex-row xl:justify-between xl:items-start gap-12 lg:gap-16">
          <div className="flex flex-col lg:flex-row gap-12 lg:gap-20 xl:gap-28">
            {/* Branding and Social */}
            <div className="flex flex-col gap-8 lg:gap-12">
              <div className="w-full max-w-80 flex flex-col gap-2">
                <div className="h-8 flex items-center gap-[3.2px]">
                  <div className="w-16 flex flex-col justify-center items-center gap-2.5">
                    <Image
                      src={branding.logo.src}
                      alt={branding.logo.alt}
                      width={branding.logo.width}
                      height={branding.logo.height}
                      className="object-contain"
                      priority
                    />
                  </div>
                  <div className="text-secondary-200 text-xl font-semibold font-['Poppins'] leading-7">
                    {sanitizeText(branding.name)}
                  </div>
                </div>
                <div className="max-w-80 text-default-300 text-base font-normal font-['Poppins'] leading-normal">
                  {sanitizeText(branding.tagline)}
                </div>
              </div>

              {/* Social Icons */}
              <div
                className="flex gap-6"
                role="list"
                aria-label="Social media links"
              >
                {socialLinks.map((social, i) => (
                  <Link
                    key={`social-${i}`}
                    href={sanitizeHref(social.href)}
                    aria-label={`Visit our ${sanitizeText(social.label)} page`}
                    role="listitem"
                    target={
                      social.href.startsWith("http") ? "_blank" : undefined
                    }
                    rel={
                      social.href.startsWith("http")
                        ? "noopener noreferrer"
                        : undefined
                    }
                  >
                    <div>{social.icon}</div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Journey Section */}
            <div className="flex flex-col gap-4">
              <div className="flex flex-col gap-2">
                <div className="text-secondary-200 text-base font-semibold font-['Poppins'] leading-normal">
                  {sanitizeText(journeySection.title)}
                </div>
                <div className="text-default-300 text-xs font-normal font-['Poppins'] leading-none">
                  {sanitizeText(journeySection.subtitle)}
                </div>
              </div>
              {journeySection.actions.map((action, i) => (
                <div key={`journey-${i}`} className="p-3 rounded-xl">
                  <Link
                    href={sanitizeHref(action.href)}
                    className="flex w-40 h-6 items-center gap-3 group focus:outline-none focus:ring-2 focus:ring-secondary-200 focus:ring-offset-2 focus:ring-offset-secondary-900 rounded"
                    aria-label={sanitizeText(action.label)}
                  >
                    <div className="flex items-center justify-center text-[24px] text-secondary-200 relative top-[1px]">
                      {action.icon}
                    </div>
                    <span className="text-default-300 text-base font-semibold font-['Poppins'] leading-normal transition-colors group-hover:text-default-500 group-focus:text-default-500">
                      {sanitizeText(action.label)}
                    </span>
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Links */}
          <div className="flex flex-col sm:flex-row sm:flex-wrap lg:flex-nowrap gap-8 sm:gap-12 lg:gap-16 xl:gap-20">
            {Object.entries(linksBySection).map(([section, links]) => (
              <div key={section} className="min-w-24 flex flex-col gap-4">
                <h3 className="text-secondary-200 text-base font-semibold font-['Poppins'] leading-normal">
                  {sanitizeText(section)}
                </h3>
                <nav aria-label={`${section} navigation`}>
                  <ul className="flex flex-col gap-3" role="list">
                    {links.map((link, i) => (
                      <li key={`${section}-${i}`} role="listitem">
                        <div className="flex items-start gap-3">
                          <Link
                            href={sanitizeHref(link.href)}
                            className="text-default-300 text-sm font-normal font-['Poppins'] leading-tight hover:underline hover:text-default-100 transition-colors focus:outline-none focus:ring-2 focus:ring-secondary-200 focus:ring-offset-2 focus:ring-offset-secondary-900 rounded"
                          >
                            {sanitizeText(link.label)}
                          </Link>
                          {link.label.toLowerCase().includes("blog") && (
                            <div className="px-2 py-0.5 bg-violet-300/50 rounded-full outline outline-1 outline-offset-[-1px] outline-violet-300/70 flex items-center">
                              <div className="text-white text-xs font-medium font-['Poppins'] leading-none">
                                New
                              </div>
                            </div>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            ))}
          </div>
        </div>

        {/* Newsletter */}
        <div className="rounded-[20px] flex flex-col lg:flex-row lg:items-center gap-8 lg:gap-16 xl:gap-24">
          <div className="flex-1 flex flex-col gap-3">
            <h2 className="text-white text-2xl sm:text-3xl lg:text-4xl font-semibold font-['Poppins'] leading-tight lg:leading-10">
              {sanitizeText(newsletter.title)}
            </h2>
            <p className="text-default-300 text-base font-normal font-['Poppins'] leading-normal">
              {sanitizeText(newsletter.description)}
            </p>
          </div>
          <div className="w-full lg:w-[460px] flex flex-col gap-2">
            <form
              action={sanitizeHref(newsletterSignupUrl)}
              method="POST"
              className="flex gap-4"
              noValidate
            >
              <div className="flex-1 pl-4 pr-2 py-2 bg-gray-50 rounded-xl outline outline-1 outline-offset-[-1px] outline-gray-300 flex items-center gap-2.5 focus-within:outline-2 focus-within:outline-secondary-200">
                <input
                  type="email"
                  name="email"
                  placeholder={sanitizeText(newsletter.placeholder)}
                  required
                  aria-label="Email address for newsletter subscription"
                  className="flex-1 bg-transparent text-gray-900 text-sm font-normal font-['Poppins'] leading-tight placeholder:text-gray-400 border-none outline-none"
                />
                <Button
                  type="submit"
                  className="min-w-24 px-5 py-1.5 bg-purple-600 text-white text-sm font-semibold font-['Poppins'] leading-tight rounded hover:bg-purple-700 focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 transition-colors"
                >
                  {sanitizeText(newsletter.buttonText)}
                </Button>
              </div>
            </form>
            <div className="flex items-center gap-0.5 text-xs">
              <span className="text-default-300 font-normal font-['Poppins'] leading-none">
                {sanitizeText(newsletter.privacyText)}{" "}
              </span>
              <Link
                href={sanitizeHref(newsletter.tosHref)}
                className="text-default-300 underline hover:text-default-100 focus:outline-none focus:ring-2 focus:ring-secondary-200 focus:ring-offset-2 focus:ring-offset-secondary-900 rounded"
              >
                TOS
              </Link>
              <span className="text-default-300 font-normal font-['Poppins'] leading-none">
                {" "}
                and{" "}
              </span>
              <Link
                href={sanitizeHref(newsletter.privacyHref)}
                className="text-default-300 underline hover:text-default-100 focus:outline-none focus:ring-2 focus:ring-secondary-200 focus:ring-offset-2 focus:ring-offset-secondary-900 rounded"
              >
                Privacy Policy
              </Link>
              <span className="text-default-300 font-normal font-['Poppins'] leading-none">
                .
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Divider */}
      <div className="h-px bg-default-500 my-8" aria-hidden="true" />

      {/* Legal Section */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="text-default-100 text-sm font-normal font-['Poppins'] leading-tight">
          {sanitizeText(copyright)}
        </div>
        <nav aria-label="Legal links">
          <ul className="flex gap-6" role="list">
            {legalLinks.map((link, i) => (
              <li key={`legal-${i}`} role="listitem">
                <Link
                  href={sanitizeHref(link.href)}
                  className="text-default-100 text-sm hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-secondary-200 focus:ring-offset-2 focus:ring-offset-secondary-900 rounded"
                >
                  {sanitizeText(link.label)}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </footer>
  );
}
