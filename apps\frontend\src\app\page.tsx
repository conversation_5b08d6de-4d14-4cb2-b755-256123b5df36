import Footer from '../components/Footer';
import Navbar from '../components/Navbar';
import SlideshowBanner from '../components/ui/SlideshowBanner';
import VerseComponent from '../components/VerseOfTheDay';
import NewsletterSubscription from '../components/NewsletterSubscription';
import StartFaithJourney from '../components/StartFaithJourney';
import { homeSlides } from '../content/HomePage';
import { Slide } from "../components/SlideshowBanner";
import Faq from "../components/Faq";

const homesSlides: Slide[] = [
  {
    title: "Let's Journey to the Truth",
    description:
      "Uncover the truth about God, His love for you, and His purpose for your life",
    imagePath: "/images/slides/journey.png",
    buttonText: "Explore the truth",
  },
  {
    title: "Discover Hope in Jesus",
    description:
      "Explore stories of faith, answers to life's toughest questions, and the hope that only Jesus can bring",
    imagePath: "/images/slides/hope.jpg",
    buttonText: "Read more",
  },
  {
    title: "Talk to Someone Who Cares",
    description:
      "Have questions? Connect with someone who will listen and guide you in your spiritual journey",
    imagePath: "/images/slides/connect.jpg",
    buttonText: "Connect with us",
  },
];

export default function Home() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      {/* Slideshow Banner */}
      <SlideshowBanner slides={homeSlides} autoSlideInterval={7000} />
      {/* Start your faith journey */}
      <StartFaithJourney />

      {/* Verse of Day */}
      <VerseOfTheDay /> {/*Now included */}

      {/* Prayer Request */}
      {/* <PrayerRequestCard /> */}

      {/* TODO: ADD COMPONENT - Featured verses */}

      {/* TODO: ADD COMPONENT - Story of the day */}

      {/* Newsletter Subscription */}
      <NewsletterSubscription />

      {/* FAQ Section */}
      <Faq />

      {/* Discipleship Card */}
      {/* <DiscipleshipCard /> */}
      <div className="flex-1"></div>
      <Footer {...footerConfig} />
    </main>
  );
}
