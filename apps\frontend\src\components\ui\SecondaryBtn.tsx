import React from 'react';
import { ChevronRight } from 'lucide-react';

interface SecondaryBtnProps {
  text: string;
  link?: string;
  color?: string;
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

export default function SecondaryBtn(SecondaryBtnProps: SecondaryBtnProps) {
  const color = SecondaryBtnProps.color || 'text-[#7828c8]';
  
  return (
    <a
      className="flex justify-center items-center gap-1.5 group cursor-pointer select-none"
      href={SecondaryBtnProps.link}
      onClick={SecondaryBtnProps.onClick}
      onMouseEnter={SecondaryBtnProps.onMouseEnter}
      onMouseLeave={SecondaryBtnProps.onMouseLeave}
    >
        
    <span className={`justify-start text-sm font-semibold font-['Poppins'] leading-tight ${color}`}>
      {SecondaryBtnProps.text}
    </span>
        
    <ChevronRight size={16} className={`${color} shrink-0 transition-transform duration-300 group-hover:translate-x-1`} />
    </a>
  );
}
